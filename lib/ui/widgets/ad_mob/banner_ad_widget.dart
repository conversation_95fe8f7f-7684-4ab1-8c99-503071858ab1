import 'package:cussme/di/general_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class BannerAdWidget extends ConsumerStatefulWidget {
  final AdSize adSize;
  final double? width;
  final double? height;

  const BannerAdWidget({
    super.key,
    this.adSize = AdSize.banner,
    this.width,
    this.height,
  });

  @override
  ConsumerState<BannerAdWidget> createState() => _BannerAdWidgetState();
}

class _BannerAdWidgetState extends ConsumerState<BannerAdWidget>
    with AutomaticKeepAliveClientMixin {
  late final BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadAd();
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  void _loadAd() {
    _bannerAd = BannerAd(
      adUnitId: ref.read(adUnitIdProvider),
      size: widget.adSize,
      request: ref.read(adRequestProvider(context)),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          setState(() {
            _isAdLoaded = true;
          });
        },
        onAdFailedToLoad: (ad, error) {
          debugPrint('Banner ad failed to load: ${error.message}');
          ad.dispose();
          setState(() {
            _isAdLoaded = false;
          });
        },
      ),
    );

    _bannerAd?.load();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    if (_bannerAd == null || !_isAdLoaded) {
      return SizedBox(
        width: widget.width ?? widget.adSize.width.toDouble(),
        height: widget.height ?? widget.adSize.height.toDouble(),
      );
    }

    return SizedBox(
      width: widget.width ?? _bannerAd.size.width.toDouble(),
      height: widget.height ?? _bannerAd.size.height.toDouble(),
      child: AdWidget(ad: _bannerAd),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
